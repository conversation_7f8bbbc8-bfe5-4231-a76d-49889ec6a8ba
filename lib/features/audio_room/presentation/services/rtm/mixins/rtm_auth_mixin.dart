import 'package:agora_rtm/agora_rtm.dart' as rtm;
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_rtm_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

import '../rtm_config.dart';

mixin RtmAuthMixin on IRtmService {
  @override
  Future<VoidResult> initialize({
    required String userId,
    required String appId,
    rtm.RtmEncryptionConfig? encryptionConfig,
  }) async {
    return executeRtmOperation(
      operation: 'initialize',
      hasJoinChannel: false,
      action: () async {
        final config = rtm.RtmConfig(
          encryptionConfig: encryptionConfig,
        );
        final (status, rtmClient) = await rtm.RTM(
          appId,
          userId,
          config: config,
        );
        if (!status.error) {
          setClient(rtmClient);
          setStorage(rtmClient.getStorage());
          setPresence(rtmClient.getPresence());
          setupEventListeners(rtmClient);
          await createAndJoinStreamChannel();
        }
        return (status, null);
      },
    );
  }

  @override
  Future<VoidResult> login(String token, String channelId) async {
    setToken(token);
    setChannelName(channelId);

    return executeRtmOperation(
      operation: 'login',
      hasJoinChannel: false,
      action: () async {
        final (loginStatus, _) = await client!.login(token);
        if (loginStatus.error) return (loginStatus, null);
        // 订阅主题消息
        // final (createStatus, channel) = await client!.createStreamChannel(channelName!);

        // 订阅点对点消息
        return await client?.subscribe(
              channelId,
              withMessage: true,
              withMetadata: true,
              withPresence: true,
            ) ??
            (RtmConfig.defaultErrorStatus, null);
      },
    );
  }

  @override
  Future<VoidResult> leaveChannel() async {
    final result = await executeRtmOperation(
      operation: 'leaveChannel',
      action: () async =>
          await client?.unsubscribe(channelName!) ??
          (RtmConfig.defaultErrorStatus, null),
    );

    if (result.isRight()) {
      removeEventListeners(client);
    }
    return result;
  }

  @override
  Future<VoidResult> logout() async {
    return executeRtmOperation(
      operation: 'logout',
      action: () async {
        final (status, _) =
            await client?.logout() ?? (RtmConfig.defaultErrorStatus, null);
        if (status.error) return (status, null);

        setChannelName(null);
        setClient(null);
        setStorage(null);
        setPresence(null);

        return (status, null);
      },
    );
  }

  @override
  Future<VoidResult> renewToken() async {
    if (token == null) {
      final leaveResult = await leaveChannel();
      if (leaveResult.isLeft()) {
        return leaveResult;
      }
      final logoutResult = await logout();
      if (logoutResult.isLeft()) {
        return logoutResult;
      }
      return const Left(AppException(
        message: 'Token is null',
        statusCode: 400,
        identifier: 'RtmService.renewToken',
      ));
    }

    return executeRtmOperation(
      operation: 'renewToken',
      action: () async =>
          await client?.renewToken(token!) ??
          (RtmConfig.defaultErrorStatus, null),
    );
  }
}
