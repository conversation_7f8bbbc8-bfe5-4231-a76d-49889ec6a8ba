import 'dart:convert';
import 'dart:typed_data';

import 'package:agora_rtm/agora_rtm.dart' as rtm;
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_rtm_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

import '../rtm_config.dart';

/// Stream Channel 管理 Mixin
/// 专门处理文字聊天相关的 Stream Channel 操作
mixin RtmStreamChannelMixin on IRtmService {
  rtm.StreamChannel? _streamChannel;
  static const String textChatTopic = 'text_chat';

  /// 获取 Stream Channel 实例
  rtm.StreamChannel? get streamChannel => _streamChannel;

  /// 设置 Stream Channel 实例
  void setStreamChannel(rtm.StreamChannel? channel) => _streamChannel = channel;

  /// 创建并加入 Stream Channel
  @override
  Future<VoidResult> createAndJoinStreamChannel() async {
    LogUtils.d('开始执行createAndJoinStreamChannel', tag: 'RtmStreamChannelMixin');
    return executeRtmOperation(
      operation: 'createAndJoinStreamChannel',
      hasJoinChannel: false,
      action: () async {
        LogUtils.d(
            'client: ${client != null}, channelName: ${channelName != null}',
            tag: 'createAndJoinStreamChannel');
        LogUtils.d('message', tag: 'createAndJoinStreamChannel');
        if (client == null || channelName == null) {
          return (RtmConfig.defaultErrorStatus, null);
        }

        // 创建 Stream Channel
        final (createStatus, channel) =
            await client!.createStreamChannel(channelName!);
        if (createStatus.error) {
          return (createStatus, null);
        }

        setStreamChannel(channel);

        // 加入 Stream Channel
        final (joinStatus, _) = await channel!.join(
          token: token,
          withPresence: true,
          withMetadata: false,
          withLock: false,
        );
        if (joinStatus.error) {
          return (joinStatus, null);
        }

        // 加入文字聊天 Topic
        final (topicStatus, _) = await channel.joinTopic(
          textChatTopic,
          qos: rtm.RtmMessageQos.ordered,
        );
        if (topicStatus.error) {
          return (topicStatus, null);
        }

        // 订阅文字聊天 Topic 以接收其他用户的消息
        final (subscribeStatus, _) = await channel.subscribeTopic(
          textChatTopic,
          users: [], // 空数组表示订阅所有用户的消息
        );

        LogUtils.d(
            'Stream Channel setup completed - join: ${!topicStatus.error}, subscribe: ${!subscribeStatus.error}',
            tag: 'RtmStreamChannelMixin');

        return (subscribeStatus, null);
      },
    );
  }

  /// 发送文字聊天消息
  @override
  Future<VoidResult> sendChatMessage(ChatMessageModel message) async {
    try {
      if (_streamChannel == null) {
        return const Left(AppException(
          message: 'Stream channel not initialized',
          statusCode: 400,
          identifier: 'sendChatMessage',
        ));
      }

      final messageJson = jsonEncode(message.toJson());
      final messageBytes = Uint8List.fromList(utf8.encode(messageJson));

      // 检查消息大小（Stream Channel 限制 1KB）
      if (messageBytes.length > 1024) {
        return const Left(AppException(
          message: 'Message size exceeds 1KB limit',
          statusCode: 400,
          identifier: 'sendChatMessage',
        ));
      }

      LogUtils.d(
          'Sending message to topic: $textChatTopic, size: ${messageBytes.length} bytes',
          tag: 'RtmStreamChannelMixin');

      try {
        await _streamChannel!.publishBinaryMessage(
          textChatTopic,
          messageBytes,
          customType: 'ChatMessage',
        );
      } catch (e) {
        LogUtils.e('E in sendChatMessage: $e', tag: 'RtmStreamChannelMixin');
        return Left(AppException(
          message: 'Error sending message: $e',
          statusCode: 400,
          identifier: 'sendChatMessage',
        ));
      }

      return const Right(null);
    } catch (e) {
      LogUtils.e('Exception in sendChatMessage: $e',
          tag: 'RtmStreamChannelMixin');
      return Left(AppException(
        message: 'Error sending message: $e',
        statusCode: 400,
        identifier: 'sendChatMessage',
      ));
    }
  }

  /// 离开 Stream Channel
  @override
  Future<VoidResult> leaveStreamChannel() async {
    return executeRtmOperation(
      operation: 'leaveStreamChannel',
      action: () async {
        if (_streamChannel == null) {
          return (
            const rtm.RtmStatus(false, '0', 'leaveStreamChannel', 'Success'),
            null
          );
        }

        // 先离开 Topic
        final (leaveTopicStatus, _) =
            await _streamChannel!.leaveTopic(textChatTopic);
        if (leaveTopicStatus.error) {
          return (leaveTopicStatus, null);
        }

        // 再离开 Channel
        final (leaveChannelStatus, _) = await _streamChannel!.leave();
        if (!leaveChannelStatus.error) {
          setStreamChannel(null);
        }

        return (leaveChannelStatus, null);
      },
    );
  }

  /// 订阅文字聊天 Topic
  @override
  Future<VoidResult> subscribeChatTopic({List<String>? users}) async {
    return executeRtmOperation(
      operation: 'subscribeChatTopic',
      action: () async {
        if (_streamChannel == null) {
          return (RtmConfig.defaultErrorStatus, null);
        }

        final (status, _) = await _streamChannel!.subscribeTopic(
          textChatTopic,
          users: users ?? [],
        );
        return (status, null);
      },
    );
  }

  /// 取消订阅文字聊天 Topic
  @override
  Future<VoidResult> unsubscribeChatTopic({List<String>? users}) async {
    return executeRtmOperation(
      operation: 'unsubscribeChatTopic',
      action: () async {
        if (_streamChannel == null) {
          return (RtmConfig.defaultErrorStatus, null);
        }

        final (status, _) = await _streamChannel!.unsubscribeTopic(
          textChatTopic,
          users: users ?? [],
        );
        return (status, null);
      },
    );
  }
}
